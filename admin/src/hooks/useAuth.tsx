import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useLayoutEffect,
  useState,
} from "react";
import { useAuthRequests } from "src/api/core/auth/mutations";
import { useUser } from "src/api/core/auth/queries";
import { getDeviceInfo } from "src/utils/getDeviceInfo";
import FingerprintJS from "@fingerprintjs/fingerprintjs";
import { useGoogleLogin } from "@react-oauth/google";
import axios from "src/api/core/base/axios-instance";
import { AxiosResponse, HttpStatusCode } from "axios";
import {
  handleResponse,
  responseError,
} from "src/api/core/base/axios-instance/interceptor";
import { useNavigate } from "react-router-dom";
import { useLocalStorage } from "usehooks-ts";

type TAuthContext = {
  user: any;
  login: (userData: any) => void;
  logout: () => void;
  signup: (data: any) => Promise<void>;
  googleLogin: () => void;
  userLogged: boolean;
};

const AuthContext = createContext<TAuthContext>({} as TAuthContext);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const { data: user } = useUser();
  const [userLogged, setUserLogged, removeUserLogged] = useLocalStorage(
    "user-logged",
    false
  );

  const navigate = useNavigate();

  const [accessToken, setAccessToken] = useState();

  const { signup, loginMutation, logout, googleLoginMutation } =
    useAuthRequests({ removeUserLogged });

  useLayoutEffect(() => {
    const resInterceptor = axios.interceptors.response.use(
      (res: AxiosResponse) => {
        const newAccessToken = res.headers["x-access-token"];
        if (newAccessToken) {
          setAccessToken(newAccessToken);
        }
        handleResponse(res);
        return res;
      },
      (error) => {
        if (
          error?.response?.status === HttpStatusCode.Unauthorized &&
          error?.response?.data?.message === "Unauthorized"
        ) {
          navigate("/login");
        }
        responseError(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(resInterceptor);
    };
  }, []);

  useLayoutEffect(() => {
    const reqInterceptor = axios.interceptors.request.use(async (config) => {
      const fingerprint = await FingerprintJS.load()
        .then((fp) => fp.get())
        .then((result) => {
          return result.visitorId;
        });
      if (fingerprint) {
        config.headers["X-Device-Fingerprint"] = fingerprint;
      }
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    });

    return () => {
      axios.interceptors.request.eject(reqInterceptor);
    };
  }, [accessToken]);

  const getReCaptchaToken = () => {
    return new Promise((resolve, reject) => {
      if (!import.meta.env.VITE_APP_RECAPTCHA_KEY) resolve(null);

      window.grecaptcha.ready(() => {
        window.grecaptcha
          .execute(import.meta.env.VITE_APP_RECAPTCHA_KEY, { action: "main" })
          .then(resolve)
          .catch(reject);
      });
    });
  };

  const login = useCallback(
    async (form: FormData) => {
      try {
        const captchaToken = await getReCaptchaToken();

        const res = await loginMutation({
          email: form.get("email"),
          password: form.get("password"),
          captchaToken,
          deviceInfo: getDeviceInfo(),
        });

        if (!res) return;

        // if (window.location.href.includes("/login")) {
        //   return;
        // }
        setAccessToken(res.accessToken);
        setUserLogged(true);
        // return { twoFA: response.data, success: true };
      } catch (error) {
        // notifyError(getErrorMessage(error));
      }
    },
    [loginMutation]
  );

  const googleLogin = useGoogleLogin({
    onSuccess: async ({ code }) => {
      const res = await googleLoginMutation({ code });

      setAccessToken(res.accessToken);
      setUserLogged(true);

      if (window.location.href.includes("/login")) {
        navigate("/dashboard");
        return;
      }
    },
    flow: "auth-code",
  });

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        signup,
        googleLogin,
        userLogged,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export function useAuth() {
  return useContext(AuthContext);
}
