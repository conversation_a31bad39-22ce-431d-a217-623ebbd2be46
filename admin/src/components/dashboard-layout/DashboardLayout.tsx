import { alpha, Box, CssBaseline } from "@mui/material";
import { ReactNode, useState } from "react";
import AppTheme from "src/theme/AppTheme";
// import {
//   //   chartsCustomizations,
//   dataGridCustomizations,
//   datePickersCustomizations,
//   treeViewCustomizations,
// } from "src/theme/customizations";
import SideBar from "./SideBar";
import { useAuth } from "src/hooks/useAuth";
import AppNavbar from "./AppNavbar";
import { APPBAR_HEIGHT } from "./config";

interface DashboardLayoutProps {
  children: ReactNode;
}

const xThemeComponents = {
  //   ...chartsCustomizations,
  // ...dataGridCustomizations,
  // ...datePickersCustomizations,
  // ...treeViewCustomizations,
};

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, userLogged } = useAuth();
  const [open, setOpen] = useState(true);
  console.log({ open });

  const toggleDrawer = () => {
    setOpen((prev) => !prev);
  };

  const closeDrawer = () => {
    setOpen(false);
  };

  return (
    <AppTheme themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      {user && userLogged ? (
        <Box
          sx={{
            display: "flex",
            position: "relative",
            overflow: "hidden",
            height: `calc(100vh - ${APPBAR_HEIGHT})`,
            marginTop: `${APPBAR_HEIGHT}`,
            width: "100vw",
          }}
        >
          <AppNavbar toggleDrawer={toggleDrawer} />

          <SideBar open={open} onClose={closeDrawer} />
          <Box
            component="main"
            sx={(theme) => ({
              flexGrow: 1,
              backgroundColor: theme.vars
                ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
                : alpha(theme.palette.background.default, 1),
              overflow: "auto",
            })}
          >
            {children}
          </Box>
        </Box>
      ) : (
        <>{children}</>
      )}
    </AppTheme>
  );
};

export default DashboardLayout;
