import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Stack from "@mui/material/Stack";
import DashboardIcon from "@mui/icons-material/Dashboard";
import BarChartIcon from "@mui/icons-material/BarChart";
import LayersIcon from "@mui/icons-material/Layers";
import RoomPreferencesIcon from "@mui/icons-material/RoomPreferences";
import { Divider } from "@mui/material";
import { useLocation } from "react-router-dom";
import { RouterNavLink } from "./RouterNavLink";

interface MenuContentProps {
  isDrawerOpen: boolean;
}

export default function SidebarContent({ isDrawerOpen }: MenuContentProps) {
  const { hash, key, pathname, search, state } = useLocation();
  console.log({ hash, key, pathname, search, state });
  return (
    <Stack
      sx={{
        flexGrow: 1,
        justifyContent: "space-between",
      }}
    >
      <List>
        <ListItem disablePadding sx={{ display: "block" }}>
          <ListItemButton
            component={RouterNavLink}
            to="/dashboard"
            selected={pathname?.includes("dashboard")}
            sx={{
              justifyContent: isDrawerOpen ? "initial" : "center",
              gap: isDrawerOpen ? 1 : "0 !important",
            }}
          >
            <ListItemIcon>
              <DashboardIcon />
            </ListItemIcon>

            <ListItemText
              primary="Dashboard"
              sx={{ opacity: isDrawerOpen ? 1 : 0 }}
            />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton
            component={RouterNavLink}
            to="/rooms"
            selected={pathname?.includes("rooms")}
            sx={{
              justifyContent: isDrawerOpen ? "initial" : "center",
              gap: isDrawerOpen ? 1 : "0 !important",
            }}
          >
            <ListItemIcon>
              <RoomPreferencesIcon />
            </ListItemIcon>
            <ListItemText
              primary="Rooms"
              sx={{ opacity: isDrawerOpen ? 1 : 0 }}
            />
          </ListItemButton>
        </ListItem>

        <Divider />

        <ListItem disablePadding>
          <ListItemButton
            sx={{
              justifyContent: isDrawerOpen ? "initial" : "center",
              gap: isDrawerOpen ? 1 : "0 !important",
            }}
          >
            <ListItemIcon>
              <BarChartIcon />
            </ListItemIcon>
            <ListItemText
              primary="Reports"
              sx={{ opacity: isDrawerOpen ? 1 : 0 }}
            />
            {/* <ExpandMoreIcon /> */}
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton
            component={RouterNavLink}
            to="/integrations"
            selected={pathname?.includes("/integrations")}
            sx={{
              justifyContent: isDrawerOpen ? "initial" : "center",
              gap: isDrawerOpen ? 1 : "0 !important",
            }}
          >
            <ListItemIcon>
              <LayersIcon />
            </ListItemIcon>
            <ListItemText
              primary="Integrations"
              sx={{ opacity: isDrawerOpen ? 1 : 0 }}
            />
          </ListItemButton>
        </ListItem>
      </List>
    </Stack>
  );
}
