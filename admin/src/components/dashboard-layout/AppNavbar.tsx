import { styled } from "@mui/material/styles";
import AppBar from "@mui/material/AppBar";
import Stack from "@mui/material/Stack";
import MuiToolbar from "@mui/material/Toolbar";
import { tabsClasses } from "@mui/material/Tabs";
import MenuRoundedIcon from "@mui/icons-material/MenuRounded";
import ColorModeButton from "src/theme/ColorModeButton";
import SelectContent from "./SelectContent";
import { IconButton } from "@mui/material";

const Toolbar = styled(MuiToolbar)({
  width: "100%",
  px: "12px",
  display: "flex",
  flexDirection: "column",
  alignItems: "start",
  justifyContent: "center",
  gap: "12px",
  flexShrink: 0,
  [`& ${tabsClasses.flexContainer}`]: {
    gap: "8px",
    px: "8px",
    pb: 0,
  },
});

export default function AppNavbar({
  toggleDrawer,
}: {
  toggleDrawer: () => void;
}) {
  return (
    <AppBar
      id="app-bar"
      position="fixed"
      sx={{
        boxShadow: 0,
        bgcolor: "background.paper",
        backgroundImage: "none",
        borderBottom: "1px solid",
        borderColor: "divider",
        top: 0,
        zIndex: 1201,
      }}
    >
      <Toolbar variant="regular">
        <Stack
          direction="row"
          sx={{
            alignItems: "center",
            flexGrow: 1,
            width: "100%",
            gap: 1,
          }}
        >
          <Stack
            direction="row"
            spacing={1}
            sx={{ justifyContent: "center", mr: "auto" }}
          >
            <IconButton aria-label="menu" size="medium" onClick={toggleDrawer}>
              <MenuRoundedIcon />
            </IconButton>
            <SelectContent />
          </Stack>
          <ColorModeButton />
        </Stack>
      </Toolbar>
    </AppBar>
  );
}
