import { useState } from "react";
// import { faEye, faEyeSlash, FAIcon } from "src/components/icons";
// import { IconButton } from "src/components/mui";

import { Input } from "../input/Input";
import { TInputProps } from "./types";
import { IconButton } from "@mui/material";

export const PasswordInput = ({
  name = "password",
  label = "Password",
  autoComplete = "current-password",
  ...other
}: TInputProps) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Input
      name={name}
      type={showPassword ? "text" : "password"}
      label={label}
      autoComplete={autoComplete}
      InputProps={{
        endAdornment: (
          <IconButton onClick={() => setShowPassword((prev) => !prev)}>
            eye
            {/* <FAIcon icon={showPassword ? faEye : faEyeSlash} /> */}
          </IconButton>
        ),
      }}
      {...other}
    />
  );
};
