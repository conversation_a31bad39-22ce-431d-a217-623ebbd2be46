import {
  Controller,
  ControllerRenderProps,
  FieldValues,
  useFormContext,
  useFormState,
} from "react-hook-form";
// import { useSetRecoilState } from "recoil";
// import { BlockedDialogType } from "src/components/blocked-dialog/BlockedDialog";
// import { FAIcon, faInfoCircle, faLock } from "src/components/icons";
// import { IconSize } from "src/components/icons/types";
import {
  Box,
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
// import { useTheme } from "src/hooks";
// import { useCheckIfLocked } from "src/hooks/useCheckIfLocked";
// import { UserSubscription } from "src/models/user";
// import { blockedDialogState } from "src/recoil/atoms/dialog";
import { MuiTelInput } from "mui-tel-input";
import { TInputProps } from "./types";

export const Input = ({
  name,
  label,
  helperText,
  type,
  maskFn,
  tooltip,
  bgColor = "#fff",
  step,
  min,
  max,
  InputProps,
  inputProps,
  onValueChange,
  // rules,
  ...other
}: TInputProps) => {
  console.log("INPUT PROPS", {
    name,
    label,
    helperText,
    type,
    maskFn,
    tooltip,
    bgColor,
    step,
    min,
    max,
    InputProps,
    inputProps,
    onValueChange,
    other,
  });
  const { setValue } = useFormContext();
  // const { errors } = useFormState();
  // const theme = useTheme();
  // const errorMessage = get(errors, `${name}.message`);
  // const primaryColor = theme.palette.primary.main;
  // const errorColor = theme.palette.error.main;
  // const borderColor = !!errorMessage ? errorColor : primaryColor;
  // const showBlockedDialog = useSetRecoilState(blockedDialogState);
  // const { checkIfLocked } = useCheckIfLocked();

  const phoneInput = (field: ControllerRenderProps<FieldValues, string>) => {
    return (
      <Box
        sx={{
          "&>div>.form-control": {
            width: "100%",
            borderRadius: "4px",
            background: "transparent",
            // "&:focus": {
            //   boxShadow: `0 0 0 1px ${borderColor}`,
            //   borderColor,
            // },
          },
          "&>div>.special-label": {
            background: bgColor,
            top: "-10px",
            left: "10px",
          },
        }}
      >
        <MuiTelInput
          {...field}
          defaultCountry="AM"
          // country={"us"}
          // isValid={!errorMessage}
        />
        {/* {errorMessage && (
        <Typography variant="caption" color="error" paddingLeft={2}>
          {errorMessage}
        </Typography>
      )} */}
      </Box>
    );
  };

  const textInput = (field: ControllerRenderProps<FieldValues, string>) => (
    <FormControl>
      <FormLabel htmlFor={field.name}>
        {label}
        <TextField
          {...field}
          variant="outlined"
          fullWidth
          // label={label}
          onChange={(event) => {
            field.onChange(event);
            if (onValueChange) {
              onValueChange(event.target.value);
            }
            if (maskFn) {
              setValue(name, maskFn(event.target.value));
            }
          }}
          // error={!!errorMessage}
          // helperText={!!errorMessage ? errorMessage : helperText}
          type={type}
          inputProps={{
            step: step || "1",
            min: min || undefined,
            max: max || undefined,
            ...inputProps,
          }}
          InputProps={{
            ...InputProps,
          }}
          {...other}
        />
      </FormLabel>
    </FormControl>
  );

  return (
    <Controller
      name={name}
      // rules={rules}
      render={({ field }) => (
        <>
          {type === "tel" ? (
            phoneInput(field)
          ) : (
            <>
              {tooltip ? (
                <Stack direction="row" width="100%">
                  {textInput(field)}
                  {/* <Tooltip sx={{ ml: 1 }} title={tooltip} placement="top">
                    <FAIcon fontSize={IconSize.Small} icon={faInfoCircle} />
                  </Tooltip> */}
                </Stack>
              ) : (
                textInput(field)
              )}
            </>
          )}
        </>
      )}
    />
  );
};
