import { yupR<PERSON>olver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from "react-hook-form";
import { Input, PasswordInput } from "src/components/form";
// import CountrySelect from "src/components/form/country-select";

import { SCHEMA } from "./schema";
import { SignupInputsType } from "./types";
import { Box, Button, Stack } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";

export function Signup() {
  // const { t } = useLocales();
  const { signup, googleLogin } = useAuth();
  const form = useForm<SignupInputsType>({
    defaultValues: {
      company: "",
      validatePassword: "",
      // country: "", TODO: Uncomment this line
      email: "",
      name: "",
      password: "",
      phone: "",
    },
    resolver: yupResolver(SCHEMA),
  });

  const { handleSubmit, formState } = form;
  const { errors } = formState;
  const onSubmit: SubmitHandler<SignupInputsType> = async (data) => {
    await signup(data);
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2}>
          <Stack
            spacing={2}
            sx={{
              overflow: "auto",
              maxHeight: "60vh",
              padding: "5px 10px",
            }}
          >
            <Input name="name" label={"Name"} autoComplete="username" />
            {/* <Input name="lastName" label={"Surname"} /> */}
            <Input name="email" label={"Email"} autoComplete="false" />
            <PasswordInput
              name="password"
              label={"Password"}
              helperText={"Password must be at least 8 characters long."}
              autoComplete="new-password"
            />
            <PasswordInput
              name="validatePassword"
              label={"Confirm password"}
              autoComplete="new-password"
            />
            <Input name="company" label={"Company"} />
            <Input
              name="phone"
              label={"Phone number"}
              type="tel"
              // bgColor={palette.grey[300]}
            />
            {/* <CountrySelect name="country" optionKey="iso" /> */}
          </Stack>
          <Box sx={{ padding: "0 10px" }}>
            <Button
              fullWidth
              size="large"
              type="submit"
              variant="contained"
              // loading={loading}
            >
              Sign up
            </Button>
            <Button onClick={() => googleLogin()}>Sign up with Google</Button>
          </Box>
        </Stack>
      </form>
    </FormProvider>
  );
}
