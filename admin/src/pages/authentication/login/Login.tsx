import { Stack, Tab, Tabs } from "@mui/material";
import { SyntheticEvent, useState } from "react";
import { TabPanel } from "src/components/tab-panel/TabPanel";
import Signup from "../register";
import LoginNew from "./LoginNew";

export const Login = () => {
  const [value, setValue] = useState(0);

  const handleChange = (_: SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Stack>
      <Tabs
        value={value}
        onChange={handleChange}
        aria-label="basic tabs example"
      >
        <Tab
          label="Login"
          id="simple-tab-0"
          aria-controls="simple-tabpanel-0"
        />
        <Tab
          label="Sign up"
          id="simple-tab-1"
          aria-controls="simple-tabpanel-1"
        />
      </Tabs>
      <TabPanel value={value} index={0}>
        <LoginNew />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <Signup />
      </TabPanel>
    </Stack>
  );
};
