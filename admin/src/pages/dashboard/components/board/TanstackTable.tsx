import { useMemo, useState, useEffect, useRef } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getExpandedRowModel,
  type ColumnDef,
  flexRender,
  ExpandedState,
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useInfiniteQuery } from "@tanstack/react-query";
import {
  Box,
  Typography,
  IconButton,
  CircularProgress,
  Collapse,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
} from "@mui/icons-material";
import { Droppable } from "../dropable-item/Droppable";
// import { generateRandomReservations } from "./utils";
import { TransitionGroup } from "react-transition-group";
import { fetchDateChunk, mockData } from "./utils";
import { Room, TableRow } from "./types";
import {
  CategoryRow,
  HeaderCell,
  StickyDataCell,
  StickyHeaderCell,
  StyledTableContainer,
} from "./styled-components";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { snapCenterToCursor } from "../../Dashboard";

export default function VirtualizedTable() {
  const [expanded, setExpanded] = useState<ExpandedState>({
    0: false,
    1: false,
    2: false,
  });
  const [roomsAndReservations, setRoomsAndReservations] = useState(mockData);
  // Initial date (today - 5 days)
  const initialDate = useMemo(() => {
    const date = new Date();
    date.setDate(date.getDate() - 5);
    return date;
  }, []);

  // Infinite query for date chunks
  const {
    data: dateChunks,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteQuery({
    queryKey: ["dateChunks"],
    queryFn: fetchDateChunk,
    initialPageParam: initialDate,
    getNextPageParam: (lastPage) => {
      const nextDate = new Date(lastPage.endDate);
      nextDate.setDate(nextDate.getDate() + 1);
      return nextDate;
    },
  });
  // Flatten all dates from all chunks
  const allDates = useMemo(() => {
    if (!dateChunks) return [];
    return dateChunks.pages.flatMap((chunk) => chunk.dates);
  }, [dateChunks]);

  // Define columns
  const columns = useMemo<ColumnDef<TableRow>[]>(() => {
    const baseColumns: ColumnDef<TableRow>[] = [
      {
        id: "room",
        header: "Room",
        size: 200,
        cell: ({ row }) => {
          const isCategory =
            "isCategory" in row.original && row.original.isCategory;
          const isExpanded = row.getIsExpanded(); //expanded[row.original.id];
          if (isCategory) {
            return (
              <Box display="flex" alignItems="center" gap={1}>
                <IconButton
                  size="small"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{ p: 0.5 }}
                >
                  {isExpanded ? (
                    <ExpandMoreIcon fontSize="small" />
                  ) : (
                    <ChevronRightIcon fontSize="small" />
                  )}
                </IconButton>
                <Typography variant="subtitle1" fontWeight="bold">
                  {row.original.name}
                </Typography>
              </Box>
            );
          }

          return (
            <Box pl={4}>
              <Typography variant="body2" fontWeight="medium">
                {row.original.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Capacity: {(row.original as Room).capacity}
              </Typography>
            </Box>
          );
        },
      },
    ];

    // Add date columns
    const dateColumns: ColumnDef<TableRow>[] = allDates.map((date) => ({
      id: date.key,
      header: () => (
        // <Box textAlign="center">
        <Typography
          display="inline-flex"
          flex={0}
          variant="caption"
          color="text.secondary"
        >
          {date.fullDate}
        </Typography>
        // </Box>
      ),
      size: 50,
    }));

    return [...baseColumns, ...dateColumns];
  }, [allDates]);
  const table = useReactTable({
    data: roomsAndReservations,
    columns,
    state: {
      expanded,
    },
    onExpandedChange: setExpanded,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSubRows: (row) => row.rooms,
    getRowId: (row) => {
      return row.isCategory ? `category_${row.id}` : `room_${row.id}`;
    },
    debugTable: true,
  });

  const { rows } = table.getRowModel();
  const parentRef = useRef<HTMLDivElement>(null);
  const columnVirtualizer = useVirtualizer({
    count: columns.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => {
      return 50;
    },
    horizontal: true,
    paddingStart: 200,
    getItemKey: (index) => {
      return allDates[index]?.key;
    },
  });
  const virtualColumns = columnVirtualizer.getVirtualItems();

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
  });
  const virtualRows = rowVirtualizer.getVirtualItems();

  // Handle infinite scroll for columns
  useEffect(() => {
    const [lastItem] = [...virtualColumns].reverse();

    if (!lastItem) return;

    if (
      lastItem.index >= columns.length - 5 && // Load when 5 columns from the end
      hasNextPage &&
      !isFetchingNextPage
    ) {
      fetchNextPage();
    }
  }, [
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    virtualColumns,
    columns.length,
  ]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  if (isLoading) {
    return (
      <Box p={4}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height={256}
        >
          <Box display="flex" alignItems="center" gap={2}>
            <CircularProgress />
            <Typography variant="h6">Loading calendar...</Typography>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <>
      <Typography variant="body2" color="text.secondary" mb={2}>
        Showing {allDates.length} days • Scroll horizontally to load more dates
      </Typography>

      <StyledTableContainer id="table-container" ref={parentRef}>
        <Box
          id="table-first-box"
          sx={{
            height: `${rowVirtualizer.getTotalSize() + 50}px`,
            width: `${columnVirtualizer.getTotalSize()}px`,
            position: "relative",
          }}
        >
          {/* Header */}
          <Box
            id="header-box"
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 10,
              height: "50px",
              width: `${columnVirtualizer.getTotalSize()}px`,
            }}
          >
            {/* Always render Room column header */}
            <StickyHeaderCell
              id="sticky-header-cell"
              sx={{
                left: 0,
                width: "200px",
                zIndex: 20,
              }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Room
              </Typography>
            </StickyHeaderCell>

            {/* Render virtualized date columns */}
            {virtualColumns.map((virtualColumn) => {
              const header =
                table.getHeaderGroups()[0]?.headers[virtualColumn.index + 1];

              return (
                <HeaderCell
                  key={virtualColumn.key}
                  sx={{
                    left: `${virtualColumn.start}px`,
                    width: `${virtualColumn.size}px`,
                    border: "1px solid yellow",
                  }}
                >
                  {header &&
                    flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                </HeaderCell>
              );
            })}
          </Box>

          {/* Rows */}
          <DndContext
            onDragEnd={({ active, over }) => {
              const { current: activeCurrent } = active.data;
              const overCurrent = over?.data?.current;
              console.log("drag end event", { overCurrent, activeCurrent });

              if (
                !!activeCurrent?.room &&
                overCurrent?.row?.isCategory &&
                activeCurrent?.roomCategory === overCurrent?.row?.id
              ) {
                console.log("TODO: remove room from reservation with request");
                setRoomsAndReservations((prev) => {
                  return prev.map((r) => {
                    if (r.id === activeCurrent?.roomCategory) {
                      return {
                        ...r,
                        reservations: [
                          ...(r.reservations || []),
                          activeCurrent,
                        ],
                        rooms: r.rooms.map((room) => {
                          if (room.id === activeCurrent?.room) {
                            return {
                              ...room,
                              reservations: room.reservations.filter(
                                (r) => r.id !== activeCurrent?.id
                              ),
                            };
                          }
                          return room;
                        }),
                      };
                    }
                    return r;
                  });
                });
              }
            }}
            onDragStart={() => console.log("drag started!!!")}
            sensors={sensors}
            modifiers={[snapCenterToCursor]}
          >
            <TransitionGroup>
              {virtualRows.map((virtualRow) => {
                const row = rows[virtualRow.index];
                const isCategory =
                  "isCategory" in row.original && row.original.isCategory;
                const roomCell = row.getVisibleCells()[0]; // Room column is always first
                return (
                  <Collapse key={row.id}>
                    <Box
                      key={virtualRow.key}
                      component={isCategory ? CategoryRow : Box}
                      sx={{
                        height: `${virtualRow.size}px`,
                        width: `${columnVirtualizer.getTotalSize()}px`,
                        display: "flex",
                        borderBottom: "1px solid #eee",
                        flexDirection: "row",
                      }}
                    >
                      {/* Always render Room column */}
                      <StickyDataCell
                        id="sticky-data-cell"
                        sx={{
                          left: 0,
                          width: "200px",
                          zIndex: 150,
                          backgroundColor: "blue",
                          flexShrink: 0,
                        }}
                      >
                        {roomCell &&
                          flexRender(
                            roomCell.column.columnDef.cell,
                            roomCell.getContext()
                          )}
                      </StickyDataCell>

                      <Box
                        sx={{
                          flexGrow: 1,
                          position: "relative",
                        }}
                      >
                        <Droppable
                          isCategory={isCategory}
                          key={row.id}
                          id={row.id}
                          row={row.original}
                          reservations={row.original.reservations || []}
                          virtualColumns={virtualColumns}
                        />
                      </Box>
                    </Box>
                  </Collapse>
                );
              })}
            </TransitionGroup>
          </DndContext>
        </Box>
      </StyledTableContainer>

      <Box mt={2} display="flex" alignItems="center" gap={2}>
        {isFetchingNextPage && (
          <Box display="flex" alignItems="center" gap={1}>
            <CircularProgress size={16} />
            <Typography variant="body2" color="text.secondary">
              Loading more dates...
            </Typography>
          </Box>
        )}
      </Box>
    </>
  );
}
