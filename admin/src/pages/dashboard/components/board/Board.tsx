import { useInfiniteQuery } from "@tanstack/react-query";
import { useVirtualizer } from "@tanstack/react-virtual";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { datelabelToDate, useColumns } from "../../hooks/useColumns";
import dayjs from "dayjs";
import { Droppable } from "../dropable-item/Droppable";
import { useCategories } from "src/api/core/rooms/queries";

const OFFSET = 5; // 5 days
const today = dayjs();

async function fetchServerPage({
  pageParam,
}: {
  pageParam: Record<string, unknown>;
}): Promise<{
  columns: Array<any>;
  nextStartDate: string | null;
  nextEndDate: string | null;
}> {
  const { endDate, startDate } = pageParam;
  const columns = Array.from(
    { length: dayjs(startDate).daysInMonth() },
    (_, i) => {
      const date = dayjs(startDate);
      const updatedDate = date.add(i, "day");
      const formattedDate = updatedDate.format("MMM DD");
      const idDate = updatedDate.format("YYYY-MM-DD");
      const yearDate = updatedDate.format("YYYY");

      return {
        id: idDate,
        date: updatedDate,
        headerName: formattedDate,
        year: yearDate,
      };
    }
  );

  await new Promise((r) => setTimeout(r, 500));
  const nextStartDate = dayjs(startDate).add(1, "month").format("YYYY-MM-DD");
  const nextEndDate = dayjs(endDate).add(1, "month").format("YYYY-MM-DD");

  return {
    columns,
    nextEndDate,
    nextStartDate,
  };
}

// TODO: delete this component eventually
export const Board = ({ rooms }) => {
  const { data: fetchedCategories } = useCategories();
  // const [rooms, setRooms] = useState([
  //   { id: 1, reservations: [] },
  //   { id: 2, reservations: [] },
  //   { id: 3, reservations: [] },
  // ]);
  // const rooms = useMemo(
  //   () => fetchedCategories?.flatMap((c) => c.rooms),
  //   [fetchedCategories]
  // );
  const parentRef = useRef<HTMLDivElement | null>(null);
  const { columns, datesRangeArray } = useColumns();
  const datesRange = useMemo(() => {
    return {
      startDate: today.subtract(2, "years"),
      endDate: today,
    };
  }, []);

  const {
    status,
    data,
    error,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ["reservations"],
    queryFn: ({ pageParam }) => {
      return fetchServerPage({ pageParam });
    },

    getNextPageParam: (data) => {
      return data.nextStartDate && data.nextEndDate
        ? { startDate: data.nextStartDate, endDate: data.nextEndDate }
        : undefined;
    },

    initialPageParam: {
      startDate: columns[0]?.id,
      endDate: columns[columns.length - 1]?.id,
    },
  });

  const allColumns = useMemo(
    () => (data ? data.pages.flatMap((d) => d.columns) : []),
    [data]
  );
  const daysCount = useMemo(() => {
    return (
      datesRange.endDate.diff(datesRange.startDate, "day") + allColumns.length
    );
  }, [allColumns.length, datesRange]);

  const rowVirtualizer = useVirtualizer({
    count: rooms?.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
    getItemKey: (index) => {
      return rooms[index]?.id;
    },
    paddingStart: 50,
  });

  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: daysCount,
    getScrollElement: () => parentRef.current,
    estimateSize: () => {
      return 50;
    },
    initialOffset: (daysCount - OFFSET) * 50,
    getItemKey: (index) => {
      return datesRangeArray[index]?.id;
    },
  });

  useEffect(() => {
    const handleScroll = async () => {
      if (parentRef.current) {
        const { scrollWidth, scrollLeft, clientWidth } = parentRef.current;
        if (scrollLeft + clientWidth >= scrollWidth) {
          fetchNextPage();
        }
      }
    };

    const scrollableElement = parentRef.current;
    if (scrollableElement) {
      scrollableElement.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollableElement) {
        scrollableElement.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  const virtualColumns = columnVirtualizer.getVirtualItems();

  return (
    allColumns.length > 0 && (
      <>
        <div
          ref={parentRef}
          className="List"
          style={{
            width: `100%`,
            overflow: "auto",
          }}
        >
          <div
            style={{
              height: `${rowVirtualizer.getTotalSize()}px`,
              width: `${columnVirtualizer.getTotalSize()}px`,
              position: "relative",
            }}
          >
            {columnVirtualizer.getVirtualItems().map((virtualColumn) => {
              return (
                <div
                  key={virtualColumn.key}
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    borderRight: "1px solid #eee",
                    borderBottom: "1px solid #eee",
                    width: `${virtualColumn.size}px`,
                    height: "100%",
                    transform: `translateX(${virtualColumn.start}px) `,
                    backgroundColor:
                      virtualColumn.key === today.format("YYYY-MM-DD")
                        ? "#34baeb"
                        : "",
                  }}
                >
                  {/* {virtualColumn.index} */}
                  {dayjs(virtualColumn.key as string).format("MMM DD")}
                  {/* {allColumns[virtualColumn.index]?.headerName} */}
                </div>
              );
            })}
            {rowVirtualizer.getVirtualItems().map((virtualRow) => {
              return (
                <div
                  key={virtualRow.key}
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    borderRight: "1px solid #eee",
                    borderBottom: "1px solid #eee",
                    //   width: `${virtualColumn.size}px`,
                    width: "100%",
                    height: `${virtualRow.size}px`,
                    transform: `translateY(${virtualRow.start}px)`,
                    // transform: `translateX(${virtualColumn.start}px) translateY(${virtualRow.start}px)`,
                  }}
                >
                  <Droppable
                    key={rooms[virtualRow.index].id}
                    id={rooms[virtualRow.index].id}
                    reservations={rooms[virtualRow.index].reservations}
                    // row={rooms[virtualRow.index]}
                    virtualColumns={virtualColumns}
                    // cellWidth={cellWidth}
                    // index={i}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </>
    )
  );
};
