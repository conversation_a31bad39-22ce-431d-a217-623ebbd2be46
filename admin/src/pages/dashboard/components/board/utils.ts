import dayjs from "dayjs";
import { DateChunk } from "./types";

const firstNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
const lastNames = ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];

function getRandomName() {
  const fn = firstNames[Math.floor(Math.random() * firstNames.length)];
  const ln = lastNames[Math.floor(Math.random() * lastNames.length)];
  return `${fn} ${ln}`;
}

export function generateRandomReservations(count, opts = {}) {
  const { roomCategory = null, room = null } = opts;

  // ←– ADJUST THESE TWO LINES TO YOUR WINDOW
  const startPeriod = dayjs("2025-07-12");
  const endPeriod = dayjs("2025-07-30");
  // ––––––––––––––––––––––––––––––––––––––––––

  const reservations = [];
  let cursor = startPeriod;
  const windowEnd = endPeriod;

  for (let i = 0; i < count; i++) {
    const remaining = count - i;
    const daysLeft = windowEnd.diff(cursor, "day");

    if (daysLeft < remaining) {
      throw new Error(
        `Not enough days left (${daysLeft}) for ${remaining} more reservation(s).`
      );
    }

    // at least 1 day, at most whatever leaves room for the rest
    const maxDuration = daysLeft - (remaining - 1);
    const duration = Math.floor(Math.random() * maxDuration) + 1;

    const resStart = cursor;
    const resEnd = resStart.add(duration, "day");

    reservations.push({
      id: i + 1,
      roomCategory,
      room,
      name: getRandomName(),
      startDate: resStart, //.format("YYYY-MM-DD"),
      endDate: resEnd, //.format("YYYY-MM-DD"),
    });

    // next one kicks off as soon as this one ends
    cursor = resEnd; //.add(1, "day");
  }

  return reservations;
}

export const generateDateChunk = (startDate: Date): DateChunk => {
  const dates = [];
  const chunkStart = new Date(startDate);

  for (let i = 0; i < 30; i++) {
    const date = new Date(chunkStart);
    date.setDate(chunkStart.getDate() + i);
    dates.push({
      key: date.toISOString().split("T")[0],
      label: date.getDate().toString(),
      fullDate: date.toLocaleDateString("en-US", {
        weekday: "short",
        // month: "short",
        day: "numeric",
      }),
    });
  }

  const endDate = new Date(chunkStart);
  endDate.setDate(chunkStart.getDate() + 29);

  return {
    dates,
    startDate: chunkStart,
    endDate,
  };
};

// Simulate API call for fetching date chunks
export const fetchDateChunk = async ({
  pageParam,
}: {
  pageParam: Date;
}): Promise<DateChunk> => {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));
  return generateDateChunk(pageParam);
};

export // Mock data with extended reservations
const mockData: RoomCategory[] = [
  // {
  //   id: 1,
  //   name: "Standard Rooms",
  //   isCategory: true,
  //   reservations: generateRandomReservations(1, {
  //     room: null,
  //     roomCategory: 1,
  //   }),
  //   rooms: [
  //     {
  //       id: 1,
  //       name: "Room 101",
  //       capacity: 2,
  //       reservations: generateRandomReservations(20, {
  //         room: 1,
  //         roomCategory: 1,
  //       }),
  //     },
  //     {
  //       id: 2,
  //       name: "Room 102",
  //       capacity: 2,
  //       reservations: generateRandomReservations(20, {
  //         room: 2,
  //         roomCategory: 1,
  //       }),
  //     },
  //     {
  //       id: 3,
  //       name: "Room 103",
  //       capacity: 2,
  //       reservations: generateRandomReservations(20, {
  //         room: 3,
  //         roomCategory: 1,
  //       }),
  //     },
  //   ],
  // },
  // {
  //   id: 2,
  //   name: "Deluxe Rooms",
  //   isCategory: true,
  //   rooms: [
  //     {
  //       id: 4,
  //       name: "Room 201",
  //       capacity: 4,
  //       reservations: generateRandomReservations(20, {
  //         room: 4,
  //         roomCategory: 2,
  //       }),
  //     },
  //     {
  //       id: 5,
  //       name: "Room 202",
  //       capacity: 4,
  //       reservations: generateRandomReservations(20, {
  //         room: 5,
  //         roomCategory: 2,
  //       }),
  //     },
  //   ],
  // },
  {
    id: 3,
    name: "Suites",
    isCategory: true,
    rooms: [
      {
        id: 6,
        name: "Presidential Suite",
        capacity: 6,
        reservations: generateRandomReservations(3, {
          room: 6,
          roomCategory: 3,
        }),
      },
    ],
  },
];
