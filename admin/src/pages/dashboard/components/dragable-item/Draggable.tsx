import { useDraggable } from "@dnd-kit/core";
import { CSS, Transform } from "@dnd-kit/utilities";
import dayjs from "dayjs";
import {
  CSSProperties,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import duration from "dayjs/plugin/duration";
import DragHandleIcon from "@mui/icons-material/DragHandle";
import { Typography } from "@mui/material";
import { resDrawerState } from "src/atoms/reservation-drawer";
import { useSetAtom } from "jotai";

dayjs.extend(duration);

type DraggableProps = {
  reservation: any;
  virtualColumns: any[];
  // cellWidth: number;
  // gridColumn?: string;
  isDragging?: boolean;
};

export function Draggable({
  reservation,
  virtualColumns,
  isDragging,
}: DraggableProps) {
  const [dragableDisabled, setDragableDisabled] = useState(false);
  const setSelectedReservation = useSetAtom(resDrawerState);

  const { attributes, listeners, setNodeRef, transform, active } = useDraggable(
    {
      id: reservation.id,
      data: reservation,
    }
  );

  const [isHovered, setIsHovered] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [gridColumnState, setGridColumnState] = useState();
  const [handleSide, setHandleSize] = useState<"left" | "right" | null>(null);
  const transformRef = useRef<Transform | null>(null);

  if (transform) transformRef.current = transform;
  const resWidth = useMemo(
    () => reservation?.endDate.diff(reservation?.startDate, "day"),
    [reservation]
  );

  const transformXPosition = useMemo(
    () => {
      // Constants with clear purpose
      const CELL_WIDTH = 50;
      const HALF_CELL = CELL_WIDTH / 2;
      const ROOM_COLUMN_OFFSET = 200;

      // Find matching column for reservation start date
      const startDateKey = reservation.startDate.format("YYYY-MM-DD");
      const matchingColumn = virtualColumns.find(
        (col) => col.key === startDateKey
      );

      // Calculate position based on visibility
      if (matchingColumn) {
        return matchingColumn.start + HALF_CELL - ROOM_COLUMN_OFFSET;
      }

      // Fallback for non-visible dates
      const firstVisibleDate = dayjs(virtualColumns[0]?.key);
      const daysFromFirstVisible = reservation.startDate.diff(
        firstVisibleDate,
        "day"
      );

      return (
        virtualColumns[0].start +
        daysFromFirstVisible * CELL_WIDTH -
        ROOM_COLUMN_OFFSET +
        HALF_CELL
      );
    }, // 25 is half the cell width and 200 is the sticky room display cell
    [virtualColumns, reservation]
  );

  const baseStyle = useMemo(
    () =>
      transform
        ? {
            transform: CSS.Translate.toString(transform),
            height: "50px",
            backgroundColor: "blue",
            width: `${resWidth * 50}px`,
          }
        : {
            height: "50px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            backgroundColor: "blue",
            zIndex: 100,
            width: `${resWidth * 50}px`,
            position: "absolute",
            transform: reservation?.room
              ? `translateX(${transformXPosition}px)`
              : "none",
          },
    [transform, resWidth, reservation?.room, transformXPosition]
  );

  const handleOnClick = useCallback(() => {
    setSelectedReservation(reservation);
    // onClick(reservation);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reservation]);

  const handleMouseEnter = useCallback(() => {
    if (reservation.room) {
      setIsHovered(true);
    }
  }, [reservation]);

  const handleMouseLeave = useCallback(() => {
    if (reservation.room) {
      setIsHovered(false);
    }
  }, [reservation]);

  const listenersOnState = useMemo(
    () => (!dragableDisabled ? { ...listeners } : undefined),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dragableDisabled]
  );
  // -------------------------------------------------------------------------------

  const handleResizeStart = (direction: "left" | "right") => {
    setIsResizing(true);
    setHandleSize(direction);
  };

  const handleResize = (e: MouseEvent) => {
    if (!isResizing) return;
    const column = e.target as HTMLElement;
    if (column && column.id && gridColumnState && handleSide) {
      const newGridColumnState =
        handleSide === "right"
          ? `${gridColumnState.split("/")[0]}/${Number(column.id) + 1}`
          : `${Number(column.id) + 1}/${gridColumnState.split("/")[1]}`;
      setGridColumnState(newGridColumnState);
    }
  };

  const handleResizeEnd = () => {
    setIsResizing(false);
    setHandleSize(null);
  };

  const style = useMemo(() => {
    return {
      ...baseStyle,
      // gridColumn: gridColumnState, // Maintain the grid placement logic
      pointerEvents: isResizing ? "none" : "auto", // Disable pointer events during resize
    };
  }, [baseStyle, isResizing]);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener("mousemove", handleResize);
      window.addEventListener("mouseup", handleResizeEnd);
    } else {
      window.removeEventListener("mousemove", handleResize);
      window.removeEventListener("mouseup", handleResizeEnd);
    }
    return () => {
      window.removeEventListener("mousemove", handleResize);
      window.removeEventListener("mouseup", handleResizeEnd);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isResizing]);

  const isHoveredOrResizing = useMemo(
    () => isHovered || isResizing,
    [isHovered, isResizing]
  );

  return (
    <button
      ref={setNodeRef}
      style={{ ...style } as CSSProperties}
      {...listenersOnState}
      {...attributes}
      onClick={handleOnClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <DragHandleIcon
        sx={{
          display: isHoveredOrResizing ? "inline-flex" : "none",
          transform: "rotate(90deg)",
          position: "absolute",
          left: -20,
          width: "50px",
          cursor: "e-resize",
        }}
        onMouseDown={() => handleResizeStart("left")}
        onMouseEnter={() => {
          setDragableDisabled(true);
        }}
        onMouseLeave={() => {
          setDragableDisabled(false);
        }}
      />

      <Typography sx={{ mx: "auto" }}>{reservation.name}</Typography>

      <DragHandleIcon
        onMouseEnter={(e) => {
          setDragableDisabled(true);
        }}
        onMouseLeave={(e) => {
          setDragableDisabled(false);
        }}
        onMouseDown={() => handleResizeStart("right")}
        sx={{
          display: isHoveredOrResizing ? "inline-flex" : "none",
          transform: "rotate(90deg)",
          position: "absolute",
          right: -20,
          width: "50px",
          cursor: "w-resize",
        }}
      />
    </button>
  );
}
