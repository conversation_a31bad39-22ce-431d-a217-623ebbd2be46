{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "6.3.1", "@emotion/babel-plugin": "11.10.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^3.10.0", "@mui/material": "^7.1.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-oauth/google": "^0.12.2", "@tanstack/react-query": "5.62.7", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@toolpad/core": "^0.15.0", "axios": "1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "1.11.13", "jotai": "2.11.0", "lucide-react": "^0.511.0", "mui-tel-input": "^2.0.0", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-hook-form": "7.54.2", "react-router-dom": "7.0.2", "react-transition-group": "^4.4.5", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "vite-tsconfig-paths": "4.2.2", "webpack": "5.89.0", "yup": "1.6.1"}, "devDependencies": {"@babel/core": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "7.23.3", "@eslint/js": "9.15.0", "@types/node": "^22.15.19", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@vitejs/plugin-react": "4.3.4", "babel-loader": "8.3.0", "eslint": "9.15.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.14", "globals": "15.12.0", "tw-animate-css": "^1.3.0", "typescript": "~5.6.2", "typescript-eslint": "8.15.0", "vite": "6.0.1"}}